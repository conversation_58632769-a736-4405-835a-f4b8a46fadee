import * as React from 'react';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  return (
    <div className={style['statistical-analysis']}>
      <div className={style['analysis-header']}>999</div>
      <div className={style['analysis-section']}>999</div>
      <div className={style['analysis-footer']}>999</div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
