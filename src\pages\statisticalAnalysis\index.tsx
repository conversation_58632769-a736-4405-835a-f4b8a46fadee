import * as React from 'react';
import { Select } from 'antd';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';

// 时间维度类型
type TimeDimension = 'year' | 'month';

// 统计周期类型
type StatisticsPeriod = 'recent3Years' | 'recent12Months' | 'recent6Months';

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  // 时间维度状态
  const [timeDimension, setTimeDimension] = React.useState<TimeDimension>('month');

  // 统计周期状态
  const [statisticsPeriod, setStatisticsPeriod] = React.useState<StatisticsPeriod>('recent12Months');

  // 时间维度选项
  const timeDimensionOptions = [
    { value: 'year', label: '年度统计' },
    { value: 'month', label: '月度统计' },
  ];

  // 根据时间维度获取统计周期选项
  const getStatisticsPeriodOptions = React.useMemo(() => {
    if (timeDimension === 'year') {
      return [
        { value: 'recent3Years', label: '近3年' }
      ];
    } else {
      return [
        { value: 'recent12Months', label: '近12个月' },
        { value: 'recent6Months', label: '近6个月' }
      ];
    }
  }, [timeDimension]);

  // 处理时间维度变更
  const handleTimeDimensionChange = (value: TimeDimension) => {
    setTimeDimension(value);
    // 根据时间维度自动设置默认的统计周期
    if (value === 'year') {
      setStatisticsPeriod('recent3Years');
    } else {
      setStatisticsPeriod('recent12Months');
    }
    // TODO: 根据时间维度更新数据
  };

  // 处理统计周期变更
  const handleStatisticsPeriodChange = (value: StatisticsPeriod) => {
    setStatisticsPeriod(value);
    // TODO: 根据统计周期更新数据
  };

  return (
    <div className={style['statistical-analysis']}>
      {/* 筛选与导出区（顶部） */}
      <div className={style['filter-export-section']}>
        <div className={style['time-dimension']}>
          {/* 时间维度选择 */}
          <span className={style.label}>时间维度：</span>
          <Select
            value={timeDimension}
            onChange={handleTimeDimensionChange}
            options={timeDimensionOptions}
            style={{ width: 120 }}
            size="middle"
          />
        </div>
        <div className={style['period-selection']}>
          {/* 统计周期选择 */}
          <span className={style.label}>统计周期：</span>
          <Select
            value={statisticsPeriod}
            onChange={handleStatisticsPeriodChange}
            options={getStatisticsPeriodOptions}
            style={{ width: 120 }}
            size="middle"
          />
        </div>
        <div className={style['export-button']}>
          {/* 月报导出按钮 */}
          <span>月报导出</span>
        </div>
      </div>

      {/* 关键指标展示区（顶部下方，卡片式） */}
      <div className={style['key-indicators-section']}>
        <div className={style['indicator-card']}>
          <span>核心指标1</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标2</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标3</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标4</span>
        </div>
      </div>

      {/* 数据可视化区（中间，分两排） */}
      <div className={style['visualization-section']}>
        {/* 第一排：能耗趋势（折线）、单品能耗（柱状 + 平均线） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能耗趋势</div>
            <div className={style['chart-content']}>
              {/* 折线图占位 */}
              <span>折线图 - 能耗趋势</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品能耗</div>
            <div className={style['chart-content']}>
              {/* 柱状图 + 平均线占位 */}
              <span>柱状图 + 平均线 - 单品能耗</span>
            </div>
          </div>
        </div>

        {/* 第二排：企业能源构成（堆叠柱）、单品排行（横向条）、能源结构（饼图） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>企业能源构成</div>
            <div className={style['chart-content']}>
              {/* 堆叠柱状图占位 */}
              <span>堆叠柱状图 - 企业能源构成</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品排行</div>
            <div className={style['chart-content']}>
              {/* 横向条形图占位 */}
              <span>横向条形图 - 单品排行</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能源结构</div>
            <div className={style['chart-content']}>
              {/* 饼图占位 */}
              <span>饼图 - 能源结构</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
