import * as React from 'react';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';

// 时间维度类型
type TimeDimension = 'year' | 'month';

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  // 时间维度状态
  const [timeDimension, setTimeDimension] = React.useState<TimeDimension>('month');

  // 时间维度选项
  const timeDimensionOptions = [
    { value: 'year', label: '年度统计' },
    { value: 'month', label: '月度统计' },
  ];

  // 处理时间维度变更
  const handleTimeDimensionChange = (value: TimeDimension) => {
    setTimeDimension(value);
    // TODO: 根据时间维度更新数据
  };

  return (
    <div className={style['statistical-analysis']}>
      {/* 筛选与导出区（顶部） */}
      <div className={style['filter-export-section']}>
        <div className={style['time-dimension']}>
          {/* 时间维度选择 */}
          <span className={style['label']}>时间维度：</span>
          <div className={style['dimension-options']}>
            {timeDimensionOptions.map((option) => (
              <button
                key={option.value}
                className={`${style['dimension-btn']} ${
                  timeDimension === option.value ? style['active'] : ''
                }`}
                onClick={() => handleTimeDimensionChange(option.value as TimeDimension)}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
        <div className={style['period-selection']}>
          {/* 统计周期选择 */}
          <span>统计周期选择</span>
        </div>
        <div className={style['export-button']}>
          {/* 月报导出按钮 */}
          <span>月报导出</span>
        </div>
      </div>

      {/* 关键指标展示区（顶部下方，卡片式） */}
      <div className={style['key-indicators-section']}>
        <div className={style['indicator-card']}>
          <span>核心指标1</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标2</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标3</span>
        </div>
        <div className={style['indicator-card']}>
          <span>核心指标4</span>
        </div>
      </div>

      {/* 数据可视化区（中间，分两排） */}
      <div className={style['visualization-section']}>
        {/* 第一排：能耗趋势（折线）、单品能耗（柱状 + 平均线） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能耗趋势</div>
            <div className={style['chart-content']}>
              {/* 折线图占位 */}
              <span>折线图 - 能耗趋势</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品能耗</div>
            <div className={style['chart-content']}>
              {/* 柱状图 + 平均线占位 */}
              <span>柱状图 + 平均线 - 单品能耗</span>
            </div>
          </div>
        </div>

        {/* 第二排：企业能源构成（堆叠柱）、单品排行（横向条）、能源结构（饼图） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>企业能源构成</div>
            <div className={style['chart-content']}>
              {/* 堆叠柱状图占位 */}
              <span>堆叠柱状图 - 企业能源构成</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品排行</div>
            <div className={style['chart-content']}>
              {/* 横向条形图占位 */}
              <span>横向条形图 - 单品排行</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能源结构</div>
            <div className={style['chart-content']}>
              {/* 饼图占位 */}
              <span>饼图 - 能源结构</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
