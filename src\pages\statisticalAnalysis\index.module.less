.statistical-analysis {
  width: 100%;
  height: 100%;
  padding: 16px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 筛选与导出区（顶部） */
.filter-export-section {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .time-dimension,
  .period-selection,
  .export-button {
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
    }
  }
}

/* 关键指标展示区（卡片式） */
.key-indicators-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;

  .indicator-card {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 数据可视化区 */
.visualization-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;

  .chart-row {
    display: flex;
    gap: 16px;
    height: 400px;

    /* 第一排：能耗趋势占2/3，单品能耗占1/3 */
    &:first-child {
      .chart-container:first-child {
        flex: 2; /* 能耗趋势占2/3 */
      }
      .chart-container:last-child {
        flex: 1; /* 单品能耗占1/3 */
      }
    }

    /* 第二排：三个图表平均分配 */
    &:last-child {
      .chart-container {
        flex: 1;
      }
    }

    .chart-container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 16px;
      display: flex;
      flex-direction: column;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #262626;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 8px;
      }

      .chart-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #8c8c8c;
        font-size: 14px;
        border: 2px dashed #d9d9d9;
        border-radius: 4px;
        background: #fafafa;
      }
    }
  }
}
